# 🚀 Hysteria2 节点提取器 v2.0 - 使用说明

## 📋 程序简介

Hysteria2 节点提取器是一个专业的 YAML 配置文件解析工具，能够自动从各种格式的 YAML 文件中提取 Hysteria2 协议节点，并转换为标准的订阅链接格式。

## ✨ 主要特性

- 🎯 **智能解析**：支持标准和紧凑 YAML 格式
- 🔄 **自动转换**：将节点信息转换为 hysteria2:// URL 格式
- 💾 **订阅格式**：生成可直接导入代理客户端的订阅文件
- 🖥️ **4K 优化**：界面针对 4K 分辨率屏幕优化，字体大小 16 号
- ⚡ **多线程处理**：界面响应流畅，不卡顿
- 📊 **实时进度**：显示处理进度和详细日志

## 🎮 使用方法

### 1. 启动程序
双击 `Hysteria2节点提取器.exe` 启动程序

### 2. 选择文件夹
- 点击 "选择文件夹" 按钮
- 选择包含 YAML 配置文件的文件夹
- 程序会自动扫描 `.yaml` 和 `.yml` 文件

### 3. 开始提取
- 点击 "🔍 开始提取" 按钮
- 程序会显示处理进度和详细日志
- 等待提取完成

### 4. 保存结果
- 提取完成后，点击 "💾 保存结果" 按钮
- 选择保存位置和文件名
- 默认保存为 `hysteria2_subscription.txt`

### 5. 使用订阅文件
- 将生成的文件内容复制到代理客户端
- 或将文件作为订阅链接使用

## 📁 支持的文件格式

### 标准 YAML 格式
```yaml
proxies:
  - name: "节点名称"
    type: hysteria2
    server: example.com
    port: 443
    password: "密码"
    sni: example.com
    skip-cert-verify: false
```

### 紧凑 YAML 格式
```yaml
proxies:
  - {name: "节点名称", type: hysteria2, server: example.com, port: 443, password: "密码"}
```

## 🔧 支持的参数

程序支持提取以下 Hysteria2 参数：
- `server`：服务器地址
- `port`：端口号
- `password`：认证密码
- `sni`：SNI 域名
- `skip-cert-verify`：跳过证书验证
- `obfs`：混淆类型
- `obfs-password`：混淆密码
- `up`：上传速度限制
- `down`：下载速度限制
- `fingerprint`：TLS 指纹

## 📊 输出格式

生成的订阅文件格式为：
```
hysteria2://密码@服务器:端口/?参数=值#节点名称
```

示例：
```
hysteria2://<EMAIL>:443/?sni=example.com&insecure=1#测试节点
```

## 🛠️ 技术规格

- **开发语言**：Python 3.x
- **GUI 框架**：Tkinter
- **解析库**：PyYAML
- **打包工具**：PyInstaller
- **支持系统**：Windows 10/11
- **屏幕优化**：4K 分辨率 (3840x2160)
- **字体大小**：16 号（4K 优化）

## 🎯 界面特性

- **现代化设计**：采用 Windows 11 风格界面
- **高 DPI 支持**：自动适配高分辨率屏幕
- **实时反馈**：进度条和状态提示
- **详细日志**：显示处理过程和结果统计
- **错误处理**：友好的错误提示和异常处理

## 📞 技术支持

如遇到问题，请检查：
1. YAML 文件格式是否正确
2. 文件是否包含 hysteria2 类型的节点
3. 文件编码是否为 UTF-8

## 📄 版本信息

- **版本**：v2.0
- **发布日期**：2024年
- **更新内容**：
  - 全新 GUI 界面
  - 4K 屏幕优化
  - 多线程处理
  - 进度条显示
  - 错误处理优化

---

💡 **提示**：程序完全离线运行，不会上传任何数据到网络。
