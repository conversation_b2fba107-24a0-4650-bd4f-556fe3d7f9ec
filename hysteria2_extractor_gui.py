#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hysteria2节点提取器 - GUI版本
针对4K屏幕优化的精美界面
"""

import os
import sys
import yaml
import urllib.parse
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import webbrowser

class Hysteria2ExtractorGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.yaml_files = []
        self.extracted_urls = []
        self.output_folder = ""

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("Hysteria2 节点提取器 v2.0 - 4K优化版")
        self.root.geometry("1400x800")  # 调整窗口大小
        self.root.minsize(1200, 700)

        # 设置窗口图标和样式 - 淡灰色背景
        self.root.configure(bg='#e8e8e8')

        # 设置DPI感知（Windows）
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # 居中显示窗口
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_styles(self):
        """设置样式主题"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置样式 - 4K屏幕优化
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei UI', 24, 'bold'),
                       foreground='#2c3e50',
                       background='#e8e8e8')

        style.configure('Subtitle.TLabel',
                       font=('Microsoft YaHei UI', 16),
                       foreground='#34495e',
                       background='#e8e8e8')

        style.configure('Custom.TButton',
                       font=('Microsoft YaHei UI', 16),
                       padding=(20, 10))

        style.configure('Status.TLabel',
                       font=('Microsoft YaHei UI', 14),
                       foreground='#27ae60',
                       background='#e8e8e8')

        style.configure('Main.TFrame',
                       background='#e8e8e8')

        style.configure('Left.TFrame',
                       background='#e8e8e8')

        style.configure('Right.TFrame',
                       background='#e8e8e8')

    def create_widgets(self):
        """创建界面组件"""
        # 主容器 - 淡灰色背景
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.configure(style='Main.TFrame')

        # 配置网格权重 - 左右布局
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=3)  # 左侧操作区域
        main_frame.columnconfigure(1, weight=2)  # 右侧结果区域
        main_frame.rowconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="🚀 Hysteria2 节点提取器", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))

        # 左侧操作区域
        left_frame = ttk.Frame(main_frame, padding="15")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 8))
        left_frame.configure(style='Left.TFrame')
        left_frame.columnconfigure(1, weight=1)
        left_frame.rowconfigure(2, weight=1)  # 让文件列表区域可扩展

        # 右侧结果区域
        right_frame = ttk.Frame(main_frame, padding="15")
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(8, 0))
        right_frame.configure(style='Right.TFrame')
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)

        # 左侧 - 文件选择区域
        file_frame = ttk.LabelFrame(left_frame, text="📁 文件选择", padding="12")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        # 文件夹选择
        self.select_folder_btn = ttk.Button(file_frame, text="选择文件夹",
                                          command=self.select_folder,
                                          style='Custom.TButton')
        self.select_folder_btn.grid(row=0, column=0, padx=(0, 8), pady=(0, 8))

        self.folder_path_var = tk.StringVar(value="")
        self.folder_path_entry = ttk.Entry(file_frame, textvariable=self.folder_path_var,
                                         font=('Microsoft YaHei UI', 11),
                                         state='readonly')
        self.folder_path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 8))

        # 单个文件选择
        self.select_file_btn = ttk.Button(file_frame, text="选择单个文件",
                                        command=self.select_single_file,
                                        style='Custom.TButton')
        self.select_file_btn.grid(row=1, column=0, padx=(0, 8), pady=(0, 8))

        self.single_file_var = tk.StringVar(value="")
        self.single_file_entry = ttk.Entry(file_frame, textvariable=self.single_file_var,
                                         font=('Microsoft YaHei UI', 11),
                                         state='readonly')
        self.single_file_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 8))

        # 输出文件夹选择
        self.select_output_btn = ttk.Button(file_frame, text="输出文件夹",
                                          command=self.select_output_folder,
                                          style='Custom.TButton')
        self.select_output_btn.grid(row=2, column=0, padx=(0, 8), pady=(0, 8))

        self.output_path_var = tk.StringVar(value="")
        self.output_path_entry = ttk.Entry(file_frame, textvariable=self.output_path_var,
                                         font=('Microsoft YaHei UI', 11),
                                         state='readonly')
        self.output_path_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 8))

        # 文件列表区域
        list_frame = ttk.LabelFrame(left_frame, text="📄 文件列表", padding="12")
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 文件列表（增加高度）
        self.files_listbox = tk.Listbox(list_frame, height=8,
                                       font=('Microsoft YaHei UI', 11),
                                       selectmode=tk.EXTENDED)
        self.files_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.files_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.files_listbox.configure(yscrollcommand=scrollbar.set)

        # 左侧 - 操作按钮区域
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)

        # 提取按钮
        self.extract_btn = ttk.Button(button_frame, text="🔍 开始提取",
                                    command=self.start_extraction,
                                    style='Custom.TButton',
                                    state='disabled')
        self.extract_btn.grid(row=0, column=0, padx=(0, 15))

        # 保存按钮
        self.save_btn = ttk.Button(button_frame, text="💾 保存结果",
                                 command=self.save_results,
                                 style='Custom.TButton',
                                 state='disabled')
        self.save_btn.grid(row=0, column=1, padx=5)

        # 快速保存按钮（直接保存到输出文件夹）
        self.quick_save_btn = ttk.Button(button_frame, text="⚡ 快速保存",
                                       command=self.quick_save_results,
                                       style='Custom.TButton',
                                       state='disabled')
        self.quick_save_btn.grid(row=0, column=2, padx=5)

        # 清空按钮
        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空结果",
                                  command=self.clear_results,
                                  style='Custom.TButton')
        self.clear_btn.grid(row=0, column=3, padx=5)

        # 左侧 - 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(left_frame,
                                          variable=self.progress_var,
                                          maximum=100)
        self.progress_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(8, 0))

        # 右侧 - 提取结果区域
        result_frame = ttk.LabelFrame(right_frame, text="📊 提取结果", padding="12")
        result_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(1, weight=1)

        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        self.status_label = ttk.Label(result_frame, textvariable=self.status_var,
                                    style='Status.TLabel')
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        # 结果文本框
        self.result_text = scrolledtext.ScrolledText(result_frame,
                                                   height=25,
                                                   font=('Consolas', 10),
                                                   wrap=tk.WORD)
        self.result_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))



    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含 YAML 文件的文件夹")
        if folder_path:
            self.folder_path_var.set(folder_path)
            # 清空单个文件选择
            self.single_file_var.set("")
            self.scan_yaml_files(folder_path)

    def scan_yaml_files(self, folder_path):
        """扫描文件夹中的 YAML 文件"""
        self.yaml_files = []
        self.files_listbox.delete(0, tk.END)

        try:
            folder = Path(folder_path)
            yaml_extensions = ['.yaml', '.yml']

            for file_path in folder.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in yaml_extensions:
                    self.yaml_files.append(file_path)
                    self.files_listbox.insert(tk.END, file_path.name)

            if self.yaml_files:
                self.status_var.set(f"找到 {len(self.yaml_files)} 个 YAML 文件")
                self.extract_btn.config(state='normal')
            else:
                self.status_var.set("未找到 YAML 文件")
                self.extract_btn.config(state='disabled')

        except Exception as e:
            messagebox.showerror("错误", f"扫描文件夹失败：{str(e)}")

    def select_single_file(self):
        """选择单个YAML文件"""
        file_path = filedialog.askopenfilename(
            title="选择 YAML 文件",
            filetypes=[("YAML 文件", "*.yaml *.yml"), ("所有文件", "*.*")]
        )
        if file_path:
            self.single_file_var.set(file_path)
            # 清空文件夹选择
            self.folder_path_var.set("")
            self.yaml_files = [Path(file_path)]
            self.files_listbox.delete(0, tk.END)
            self.files_listbox.insert(tk.END, Path(file_path).name)
            self.status_var.set("已选择单个文件")
            self.extract_btn.config(state='normal')

    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出文件夹")
        if folder_path:
            self.output_folder = folder_path
            self.output_path_var.set(folder_path)
        else:
            self.output_folder = ""
            self.output_path_var.set("")

    def start_extraction(self):
        """开始提取节点"""
        if not self.yaml_files:
            messagebox.showwarning("警告", "请先选择包含 YAML 文件的文件夹")
            return

        # 禁用按钮
        self.extract_btn.config(state='disabled')
        self.status_var.set("正在提取节点...")
        self.result_text.delete(1.0, tk.END)

        # 在新线程中执行提取
        thread = threading.Thread(target=self.extract_nodes)
        thread.daemon = True
        thread.start()

    def extract_nodes(self):
        """提取节点的核心逻辑"""
        try:
            self.extracted_urls = []
            total_nodes = 0
            successful_conversions = 0

            for i, file_path in enumerate(self.yaml_files):
                # 更新进度条和状态
                self.root.after(0, self.update_progress, i, len(self.yaml_files))
                self.root.after(0, lambda: self.status_var.set(f"处理文件 {i+1}/{len(self.yaml_files)}: {file_path.name}"))

                # 加载 YAML 文件
                data = self.load_yaml_file(file_path)
                if not data:
                    self.log_message(f"❌ 文件解析失败: {file_path.name}")
                    continue

                # 提取 hysteria2 节点
                hysteria2_nodes = self.extract_hysteria2_nodes(data)
                if not hysteria2_nodes:
                    self.log_message(f"ℹ️  {file_path.name}: 未找到 hysteria2 节点")
                    continue

                self.log_message(f"✅ {file_path.name}: 找到 {len(hysteria2_nodes)} 个 hysteria2 节点")
                total_nodes += len(hysteria2_nodes)

                # 转换为 URL 格式
                for j, node in enumerate(hysteria2_nodes, 1):
                    url = self.convert_to_url(node)
                    if url:
                        self.extracted_urls.append(url)
                        successful_conversions += 1
                        self.log_message(f"    ✓ 节点 {j}: {node.get('name', '未命名')}")
                    else:
                        self.log_message(f"    ✗ 节点 {j}: 转换失败")

            # 完成进度条
            self.root.after(0, self.update_progress, len(self.yaml_files), len(self.yaml_files))

            # 完成提取
            self.root.after(0, self.extraction_completed, total_nodes, successful_conversions)

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"提取过程中发生错误：{str(e)}"))
            self.root.after(0, lambda: self.extract_btn.config(state='normal'))

    def extraction_completed(self, total_nodes, successful_conversions):
        """提取完成后的处理"""
        if self.extracted_urls:
            self.status_var.set(f"✅ 提取完成！共提取到 {len(self.extracted_urls)} 个有效节点")
            self.save_btn.config(state='normal')
            self.quick_save_btn.config(state='normal')

            # 显示统计信息
            self.log_message("\n" + "="*50)
            self.log_message(f"🎉 提取完成！")
            self.log_message(f"📊 处理文件: {len(self.yaml_files)} 个")
            self.log_message(f"📈 总节点数: {total_nodes} 个")
            self.log_message(f"✅ 成功转换: {successful_conversions} 个")
            self.log_message("="*50)

        else:
            self.status_var.set("❌ 未找到有效的 hysteria2 节点")

        self.extract_btn.config(state='normal')

    def log_message(self, message):
        """在结果文本框中添加日志消息"""
        def update_text():
            self.result_text.insert(tk.END, message + "\n")
            self.result_text.see(tk.END)

        self.root.after(0, update_text)

    def show_about(self):
        """显示关于对话框"""
        about_text = """
🚀 Hysteria2 节点提取器 v2.0

✨ 功能特性：
• 支持标准和紧凑 YAML 格式
• 自动提取 Hysteria2 协议节点
• 生成标准订阅链接格式
• 4K 屏幕优化界面
• 多线程处理，界面不卡顿

🛠️ 技术栈：
• Python 3.x + Tkinter
• PyYAML 解析库
• 现代化 GUI 设计

📧 联系方式：
• 项目开源，欢迎贡献代码
• 支持 Windows / macOS / Linux

© 2024 Hysteria2 Tools
        """

        messagebox.showinfo("关于 Hysteria2 节点提取器", about_text)

    def update_progress(self, current, total):
        """更新进度条"""
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)
        else:
            self.progress_var.set(0)

    def save_results(self):
        """保存提取结果"""
        if not self.extracted_urls:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        # 确定初始目录 - 优先使用用户指定的输出文件夹
        if self.output_folder and os.path.exists(self.output_folder):
            initial_dir = self.output_folder
        else:
            initial_dir = os.path.expanduser("~/Desktop")  # 默认桌面
            if not os.path.exists(initial_dir):
                initial_dir = os.getcwd()

        # 生成默认文件名（包含时间戳）
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"hysteria2_subscription_{timestamp}.txt"

        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            title="保存 Hysteria2 订阅文件",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("订阅文件", "*.sub"),
                ("所有文件", "*.*")
            ],
            initialvalue=default_filename,
            initialdir=initial_dir
        )

        if file_path:
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    for url in self.extracted_urls:
                        f.write(url + '\n')

                # 验证文件是否真的保存成功
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    messagebox.showinfo("保存成功",
                        f"✅ 已成功保存 {len(self.extracted_urls)} 个节点\n"
                        f"📁 保存位置: {file_path}\n"
                        f"📊 文件大小: {file_size} 字节")

                    self.status_var.set(f"✅ 已保存到: {Path(file_path).name}")
                    self.log_message(f"✅ 文件已保存: {file_path}")

                    # 询问是否打开文件夹
                    if messagebox.askyesno("打开文件夹", "是否打开保存文件的文件夹？"):
                        import subprocess
                        subprocess.run(['explorer', '/select,', file_path], shell=True)
                else:
                    messagebox.showerror("保存失败", "文件保存失败，请检查路径权限")

            except PermissionError:
                messagebox.showerror("权限错误", f"没有权限写入文件:\n{file_path}\n请选择其他位置或以管理员身份运行")
            except Exception as e:
                messagebox.showerror("保存错误", f"保存文件时发生错误:\n{str(e)}\n请重试或选择其他位置")

    def quick_save_results(self):
        """快速保存到指定输出文件夹"""
        if not self.extracted_urls:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        # 检查是否设置了输出文件夹
        if not self.output_folder or not os.path.exists(self.output_folder):
            messagebox.showwarning("警告", "请先选择输出文件夹")
            return

        try:
            # 生成文件名（包含时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hysteria2_subscription_{timestamp}.txt"
            file_path = os.path.join(self.output_folder, filename)

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                for url in self.extracted_urls:
                    f.write(url + '\n')

            # 验证文件是否保存成功
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                messagebox.showinfo("快速保存成功",
                    f"✅ 已快速保存 {len(self.extracted_urls)} 个节点\n"
                    f"📁 保存位置: {file_path}\n"
                    f"📊 文件大小: {file_size} 字节")

                self.status_var.set(f"✅ 已快速保存到: {filename}")
                self.log_message(f"⚡ 快速保存完成: {file_path}")

                # 询问是否打开文件夹
                if messagebox.askyesno("打开文件夹", "是否打开保存文件的文件夹？"):
                    import subprocess
                    subprocess.run(['explorer', '/select,', file_path], shell=True)
            else:
                messagebox.showerror("保存失败", "快速保存失败，请检查输出文件夹权限")

        except PermissionError:
            messagebox.showerror("权限错误", f"没有权限写入输出文件夹:\n{self.output_folder}\n请选择其他位置或以管理员身份运行")
        except Exception as e:
            messagebox.showerror("保存错误", f"快速保存时发生错误:\n{str(e)}")

    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.extracted_urls = []
        self.save_btn.config(state='disabled')
        self.quick_save_btn.config(state='disabled')
        self.status_var.set("已清空结果")

    def load_yaml_file(self, file_path):
        """加载YAML文件，兼容标准和紧凑格式"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            try:
                data = yaml.safe_load(content)
                return data
            except yaml.YAMLError as e:
                self.log_message(f"解析YAML文件失败 {file_path.name}: {e}")
                return None

        except Exception as e:
            self.log_message(f"读取文件失败 {file_path.name}: {e}")
            return None

    def extract_hysteria2_nodes(self, data):
        """从YAML数据中提取hysteria2节点"""
        hysteria2_nodes = []

        if not data:
            return hysteria2_nodes

        # 查找proxies字段
        proxies = data.get('proxies', [])
        if not isinstance(proxies, list):
            return hysteria2_nodes

        for proxy in proxies:
            if isinstance(proxy, dict) and proxy.get('type') == 'hysteria2':
                hysteria2_nodes.append(proxy)

        return hysteria2_nodes

    def convert_to_url(self, node):
        """将hysteria2节点转换为URL格式"""
        try:
            # 必需字段
            password = node.get('password', '')
            server = node.get('server', '')
            port = node.get('port', 443)
            name = node.get('name', '')

            if not password or not server:
                return None

            # 构建基础URL
            url = f"hysteria2://{password}@{server}:{port}/"

            # 构建查询参数
            params = {}

            # SNI
            sni = node.get('sni', '')
            if sni:
                params['sni'] = sni

            # 跳过证书验证
            skip_cert_verify = node.get('skip-cert-verify', False)
            if skip_cert_verify:
                params['insecure'] = '1'

            # 混淆
            obfs = node.get('obfs', '')
            if obfs:
                params['obfs'] = obfs

            # 混淆密码
            obfs_password = node.get('obfs-password', '')
            if obfs_password:
                params['obfs-password'] = obfs_password

            # 上传速度限制
            up = node.get('up', '')
            if up:
                params['up'] = up

            # 下载速度限制
            down = node.get('down', '')
            if down:
                params['down'] = down

            # 指纹
            fingerprint = node.get('fingerprint', '')
            if fingerprint:
                params['fingerprint'] = fingerprint

            # 添加查询参数
            if params:
                query_string = urllib.parse.urlencode(params)
                url += f"?{query_string}"

            # 添加节点名称作为fragment
            if name:
                encoded_name = urllib.parse.quote(name, safe='')
                url += f"#{encoded_name}"

            return url

        except Exception as e:
            return None


def main():
    """主函数"""
    root = tk.Tk()
    app = Hysteria2ExtractorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
