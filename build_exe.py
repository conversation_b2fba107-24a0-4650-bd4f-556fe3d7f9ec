#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hysteria2节点提取器打包脚本
使用PyInstaller将GUI程序打包为exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import yaml
        import tkinter
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['hysteria2_extractor_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['yaml', 'urllib.parse'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Hysteria2节点提取器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file='version_info.txt'
)
'''

    with open('hysteria2_extractor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已创建规格文件")

def create_version_info():
    """创建版本信息文件"""
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2,0,0,0),
    prodvers=(2,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'Hysteria2 Tools'),
        StringStruct(u'FileDescription', u'Hysteria2节点提取器'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'Hysteria2Extractor'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'Hysteria2节点提取器.exe'),
        StringStruct(u'ProductName', u'Hysteria2节点提取器'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''

    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    print("✅ 已创建版本信息文件")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")

    # 清理之前的构建
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')

    # 使用PyInstaller构建
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'hysteria2_extractor.spec'
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 Hysteria2节点提取器 - 打包工具")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        return

    # 检查源文件
    if not os.path.exists('hysteria2_extractor_gui.py'):
        print("❌ 找不到源文件: hysteria2_extractor_gui.py")
        return

    # 创建必要文件
    create_spec_file()
    create_version_info()

    # 构建exe
    if build_exe():
        exe_path = Path('dist') / 'Hysteria2节点提取器.exe'
        if exe_path.exists():
            print(f"🎉 打包完成！")
            print(f"📁 exe文件位置: {exe_path.absolute()}")
            print(f"📊 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
        else:
            print("❌ exe文件未找到")

    # 清理临时文件
    temp_files = ['hysteria2_extractor.spec', 'version_info.txt']
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)

    if os.path.exists('build'):
        shutil.rmtree('build')

if __name__ == "__main__":
    main()
