@echo off
chcp 65001 >nul
echo 🚀 创建 Hysteria2 节点提取器发布包
echo ========================================

:: 创建发布目录
if not exist "发布包" mkdir "发布包"

:: 复制主程序
echo 📦 复制主程序...
copy "dist\Hysteria2节点提取器.exe" "发布包\"

:: 复制说明文档
echo 📄 复制说明文档...
copy "使用说明.md" "发布包\"

:: 创建示例文件夹
echo 📁 创建示例文件夹...
if not exist "发布包\示例文件" mkdir "发布包\示例文件"

:: 创建示例 YAML 文件
echo 📝 创建示例文件...
echo proxies: > "发布包\示例文件\示例配置.yaml"
echo   - name: "示例节点1" >> "发布包\示例文件\示例配置.yaml"
echo     type: hysteria2 >> "发布包\示例文件\示例配置.yaml"
echo     server: example1.com >> "发布包\示例文件\示例配置.yaml"
echo     port: 443 >> "发布包\示例文件\示例配置.yaml"
echo     password: "password123" >> "发布包\示例文件\示例配置.yaml"
echo     sni: example1.com >> "发布包\示例文件\示例配置.yaml"
echo   - name: "示例节点2" >> "发布包\示例文件\示例配置.yaml"
echo     type: hysteria2 >> "发布包\示例文件\示例配置.yaml"
echo     server: example2.com >> "发布包\示例文件\示例配置.yaml"
echo     port: 8443 >> "发布包\示例文件\示例配置.yaml"
echo     password: "password456" >> "发布包\示例文件\示例配置.yaml"
echo     skip-cert-verify: true >> "发布包\示例文件\示例配置.yaml"

echo ✅ 发布包创建完成！
echo 📁 位置: %cd%\发布包
echo.
echo 📋 包含文件:
echo   - Hysteria2节点提取器.exe (主程序)
echo   - 使用说明.md (详细说明)
echo   - 示例文件\示例配置.yaml (测试文件)
echo.
pause
