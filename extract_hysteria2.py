#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hysteria2节点提取器
功能：
1. 读取当前文件夹中的所有YAML格式文件
2. 提取其中属于hysteria2协议的代理节点
3. 转换为URL格式节点
4. 兼容标准和紧凑的YAML格式
"""

import os
import yaml
import urllib.parse
from pathlib import Path
import sys

def load_yaml_file(file_path):
    """
    加载YAML文件，兼容标准和紧凑格式
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 尝试解析YAML
        try:
            data = yaml.safe_load(content)
            return data
        except yaml.YAMLError as e:
            print(f"解析YAML文件失败 {file_path}: {e}")
            return None

    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None

def extract_hysteria2_nodes(data):
    """
    从YAML数据中提取hysteria2节点
    """
    hysteria2_nodes = []

    if not data:
        return hysteria2_nodes

    # 查找proxies字段
    proxies = data.get('proxies', [])
    if not isinstance(proxies, list):
        return hysteria2_nodes

    for proxy in proxies:
        if isinstance(proxy, dict) and proxy.get('type') == 'hysteria2':
            hysteria2_nodes.append(proxy)

    return hysteria2_nodes

def convert_to_url(node):
    """
    将hysteria2节点转换为URL格式
    格式: hysteria2://password@server:port/?params#name
    """
    try:
        # 必需字段
        password = node.get('password', '')
        server = node.get('server', '')
        port = node.get('port', 443)
        name = node.get('name', '')

        if not password or not server:
            print(f"节点缺少必需字段: {node}")
            return None

        # 构建基础URL
        url = f"hysteria2://{password}@{server}:{port}/"

        # 构建查询参数
        params = {}

        # SNI
        sni = node.get('sni', '')
        if sni:
            params['sni'] = sni

        # 跳过证书验证
        skip_cert_verify = node.get('skip-cert-verify', False)
        if skip_cert_verify:
            params['insecure'] = '1'

        # 混淆
        obfs = node.get('obfs', '')
        if obfs:
            params['obfs'] = obfs

        # 混淆密码
        obfs_password = node.get('obfs-password', '')
        if obfs_password:
            params['obfs-password'] = obfs_password

        # 上传速度限制
        up = node.get('up', '')
        if up:
            params['up'] = up

        # 下载速度限制
        down = node.get('down', '')
        if down:
            params['down'] = down

        # 指纹
        fingerprint = node.get('fingerprint', '')
        if fingerprint:
            params['fingerprint'] = fingerprint

        # 添加查询参数
        if params:
            query_string = urllib.parse.urlencode(params)
            url += f"?{query_string}"

        # 添加节点名称作为fragment
        if name:
            encoded_name = urllib.parse.quote(name, safe='')
            url += f"#{encoded_name}"

        return url

    except Exception as e:
        print(f"转换节点失败: {e}")
        return None

def find_yaml_files(directory='.'):
    """
    查找目录中的所有YAML文件
    """
    yaml_files = []
    directory_path = Path(directory)

    # 支持的YAML文件扩展名
    yaml_extensions = ['.yaml', '.yml']

    for file_path in directory_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in yaml_extensions:
            yaml_files.append(file_path)

    return yaml_files

def main():
    """
    主函数
    """
    print("Hysteria2节点提取器")
    print("=" * 50)
    print("功能：从YAML文件中提取hysteria2协议节点并转换为URL格式")
    print("支持：标准YAML格式和紧凑YAML格式")
    print("=" * 50)

    # 查找当前目录下的所有YAML文件
    yaml_files = find_yaml_files()

    if not yaml_files:
        print("❌ 当前目录下没有找到YAML文件")
        print("请确保当前目录包含 .yaml 或 .yml 格式的文件")
        return

    print(f"📁 找到 {len(yaml_files)} 个YAML文件:")
    for file_path in yaml_files:
        print(f"  - {file_path.name}")
    print()

    all_hysteria2_urls = []
    total_processed = 0

    # 处理每个YAML文件
    for file_path in yaml_files:
        print(f"🔍 处理文件: {file_path.name}")

        # 加载YAML数据
        data = load_yaml_file(file_path)
        if not data:
            print(f"  ❌ 文件解析失败，跳过")
            continue

        # 提取hysteria2节点
        hysteria2_nodes = extract_hysteria2_nodes(data)

        if not hysteria2_nodes:
            print(f"  ℹ️  未找到hysteria2节点")
            continue

        print(f"  ✅ 找到 {len(hysteria2_nodes)} 个hysteria2节点")

        # 转换为URL格式
        successful_conversions = 0
        for i, node in enumerate(hysteria2_nodes, 1):
            url = convert_to_url(node)
            if url:
                all_hysteria2_urls.append(url)
                successful_conversions += 1
                print(f"    ✓ 节点 {i}: {node.get('name', '未命名')}")
            else:
                print(f"    ✗ 节点 {i}: 转换失败")

        total_processed += len(hysteria2_nodes)
        print(f"  📊 成功转换: {successful_conversions}/{len(hysteria2_nodes)}")
        print()

    # 输出结果
    if all_hysteria2_urls:
        print("🎉 提取完成！")
        print(f"📈 总共提取到 {len(all_hysteria2_urls)} 个有效的hysteria2节点")
        print("=" * 50)
        print("🔗 Hysteria2 URL格式节点:")
        print()

        for i, url in enumerate(all_hysteria2_urls, 1):
            print(f"{i:2d}. {url}")

        # 保存到订阅格式文件
        subscription_file = "hysteria2_subscription.txt"
        try:
            with open(subscription_file, 'w', encoding='utf-8') as f:
                for url in all_hysteria2_urls:
                    f.write(url + '\n')
            print(f"\n💾 节点已保存到文件: {subscription_file}")
            print(f"📋 可直接复制内容到代理客户端或用作订阅链接")

        except Exception as e:
            print(f"\n❌ 保存文件失败: {e}")
    else:
        print("❌ 没有找到有效的hysteria2节点")
        print("请检查YAML文件是否包含hysteria2协议的代理配置")

if __name__ == "__main__":
    main()
